# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 数据库配置
DATABASE_URL=sqlite:///./daily_quotes.db

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
# DEBUG=True 开发环境，启用CORS跨域支持和代码热重载
# DEBUG=False 生产环境，禁用CORS和热重载，提高安全性
DEBUG=True

# 定时任务配置
QUOTE_GENERATION_HOUR=23
QUOTE_GENERATION_MINUTE=0

# 安全配置
# 是否启用手动生成语录接口 (True=启用, False=禁用)
# 建议在生产环境中设置为False，避免接口被滥用
ENABLE_MANUAL_GENERATION=True

# 开发环境说明
# 本项目采用前后端分离架构
# 前端为纯静态文件，位于 frontend/ 目录
# 开发时可直接打开 frontend/index.html 或使用 Docker 部署
